'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Input,
  message,
  Modal,
  Descriptions,
  Switch,
  Popconfirm,
  Alert,
} from 'antd'
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  <PERSON>Outlined,
  DatabaseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import Link from 'next/link'

const { Title, Text } = Typography
const { Search } = Input

interface FormConfig {
  id: number
  formId: string
  formName: string
  isActive: boolean
  fieldCount: number
  webhookUrl: string
  tableName: string
  createdAt: string
  updatedAt: string
}

interface FormListResponse {
  success: boolean
  data: {
    forms: FormConfig[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

export default function FormsPage() {
  const [forms, setForms] = useState<FormConfig[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [selectedForm, setSelectedForm] = useState<FormConfig | null>(null)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  // 获取表单列表
  const fetchForms = async (page = 1, search = '') => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.pageSize.toString(),
        ...(search && { search }),
      })

      const response = await fetch(`/api/forms?${params}`)
      const result: FormListResponse = await response.json()

      if (result.success) {
        setForms(result.data.forms)
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total,
        })
      } else {
        message.error('获取表单列表失败')
      }
    } catch (error) {
      console.error('获取表单列表失败:', error)
      message.error('获取表单列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value)
    fetchForms(1, value)
  }

  // 切换表单状态
  const toggleFormStatus = async (formId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/forms/${formId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive }),
      })

      const result = await response.json()

      if (result.success) {
        message.success(`表单已${isActive ? '启用' : '禁用'}`)
        fetchForms(pagination.current, searchText)
      } else {
        message.error(result.error || '操作失败')
      }
    } catch (error) {
      console.error('切换表单状态失败:', error)
      message.error('操作失败')
    }
  }

  // 删除表单配置
  const deleteForm = async (formId: string) => {
    try {
      const response = await fetch(`/api/forms/${formId}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        message.success('表单配置删除成功')
        fetchForms(pagination.current, searchText)
      } else {
        message.error(result.error || '删除失败')
      }
    } catch (error) {
      console.error('删除表单配置失败:', error)
      message.error('删除失败')
    }
  }

  // 复制Webhook URL
  const copyWebhookUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      message.success('Webhook URL已复制到剪贴板')
    } catch (error) {
      message.error('复制失败')
    }
  }

  // 查看详情
  const showDetail = (form: FormConfig) => {
    setSelectedForm(form)
    setDetailModalVisible(true)
  }

  useEffect(() => {
    fetchForms()
  }, [])

  const columns: ColumnsType<FormConfig> = [
    {
      title: '表单ID',
      dataIndex: 'formId',
      width: 120,
      render: (formId) => <Text code>{formId}</Text>,
    },
    {
      title: '表单名称',
      dataIndex: 'formName',
      ellipsis: {
        showTitle: false,
      },
      render: (name) => (
        <Tooltip title={name}>
          <Text>{name}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 80,
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          size="small"
          onChange={(checked) => toggleFormStatus(record.formId, checked)}
        />
      ),
    },
    {
      title: '字段数',
      dataIndex: 'fieldCount',
      width: 80,
      render: (count) => <Tag color="blue">{count}</Tag>,
    },
    {
      title: 'Webhook URL',
      dataIndex: 'webhookUrl',
      width: 200,
      render: (url) => (
        <div className="flex items-center space-x-2">
          <Text code ellipsis style={{ maxWidth: 120 }}>
            {url}
          </Text>
          <Tooltip title="复制URL">
            <Button
              type="text"
              size="small"
              icon={<LinkOutlined />}
              onClick={() => copyWebhookUrl(url)}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      render: (date) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => showDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑配置">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => message.info('编辑功能开发中')}
            />
          </Tooltip>
          <Popconfirm
            title="确认删除"
            description="删除后将无法恢复，确定要删除这个表单配置吗？"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            onConfirm={() => deleteForm(record.formId)}
            okText="确认删除"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="删除配置">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-4">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <Title level={2} className="mb-1">
            表单管理
          </Title>
          <Text type="secondary">
            管理金数据表单配置，查看Webhook接收状态
          </Text>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => fetchForms(pagination.current, searchText)}
            loading={loading}
          >
            刷新
          </Button>
          <Link href="/dashboard/forms/config">
            <Button type="primary" icon={<PlusOutlined />}>
              新建表单配置
            </Button>
          </Link>
        </Space>
      </div>

      {/* 搜索和筛选 */}
      <Card size="small">
        <div className="flex justify-between items-center">
          <Search
            placeholder="搜索表单ID或名称"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            loading={loading}
            enterButton={<SearchOutlined />}
          />
          <div>
            <Text type="secondary">
              共 {pagination.total} 个表单配置
            </Text>
          </div>
        </div>
      </Card>

      {/* 表单列表 */}
      <Card>
        {forms.length === 0 && !loading ? (
          <div className="text-center py-8">
            <DatabaseOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
            <div className="mt-4">
              <Text type="secondary">暂无表单配置</Text>
              <div className="mt-2">
                <Link href="/dashboard/forms/config">
                  <Button type="primary" icon={<PlusOutlined />}>
                    创建第一个表单配置
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <Table
            columns={columns}
            dataSource={forms}
            rowKey="formId"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                if (pageSize !== pagination.pageSize) {
                  setPagination({ ...pagination, pageSize })
                }
                fetchForms(page, searchText)
              },
            }}
            scroll={{ x: 1000 }}
            size="small"
          />
        )}
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="表单配置详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {selectedForm && (
          <div className="space-y-4">
            <Alert
              message="配置信息"
              description="以下是表单的详细配置信息和统计数据"
              type="info"
              showIcon
            />
            
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="表单ID" span={1}>
                <Text code>{selectedForm.formId}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="表单名称" span={1}>
                {selectedForm.formName}
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={1}>
                <Tag color={selectedForm.isActive ? 'green' : 'red'}>
                  {selectedForm.isActive ? '启用中' : '已禁用'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="字段数量" span={1}>
                <Tag color="blue">{selectedForm.fieldCount} 个</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="数据表名" span={2}>
                <Text code>{selectedForm.tableName}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Webhook URL" span={2}>
                <div className="flex items-center space-x-2">
                  <Text code className="flex-1">
                    {selectedForm.webhookUrl}
                  </Text>
                  <Button
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() => copyWebhookUrl(selectedForm.webhookUrl)}
                  >
                    复制
                  </Button>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={1}>
                {new Date(selectedForm.createdAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间" span={1}>
                {new Date(selectedForm.updatedAt).toLocaleString('zh-CN')}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  )
}